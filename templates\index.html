<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快乐8数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .number-badge {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .hot-number {
            background: #dc3545;
        }
        .cold-number {
            background: #6c757d;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .btn-refresh {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
        }
        .btn-refresh:hover {
            background: linear-gradient(135deg, #43a3f5 0%, #00e8f5 100%);
            color: white;
        }

        /* 号码选择器样式 */
        .number-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            max-height: 300px;
            overflow-y: auto;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }

        .number-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #6c757d;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
            user-select: none;
        }

        .number-circle:hover {
            border-color: #007bff;
            background-color: #e3f2fd;
            transform: scale(1.1);
        }

        .number-circle.selected {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-color: #0056b3;
            transform: scale(1.1);
        }

        .number-circle.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .number-circle.disabled:hover {
            transform: none;
            border-color: #6c757d;
            background-color: white;
        }

        /* 匹配结果样式 */
        .match-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .match-full {
            background-color: #28a745;
            color: white;
        }

        .match-partial {
            background-color: #ffc107;
            color: #212529;
        }

        .match-none {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                快乐8数据分析系统
            </a>
            <button class="btn btn-refresh" onclick="refreshData()">
                <i class="fas fa-sync-alt me-1"></i>
                刷新数据
            </button>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 基本统计信息 -->
        <div class="row" id="statsSection">
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <h3 id="totalPeriods">-</h3>
                    <p>总期数</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <h3 id="latestPeriod">-</h3>
                    <p>最新期号</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <h3 id="earliestPeriod">-</h3>
                    <p>最早期号</p>
                </div>
            </div>
        </div>

        <!-- 最新开奖结果 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-trophy me-2"></i>
                最新开奖结果
            </div>
            <div class="card-body">
                <div id="latestResults" class="loading">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row">
            <!-- 号码频率图表 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-bar me-2"></i>
                        号码出现频率 TOP 20
                    </div>
                    <div class="card-body">
                        <div id="frequencyChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>

            <!-- 最近趋势图表 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-line me-2"></i>
                        最近50期热门号码
                    </div>
                    <div class="card-body">
                        <div id="trendChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 号码分析区域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-search me-2"></i>
                        号码分析工具
                    </div>
                    <div class="card-body">
                        <!-- 号码选择区域 -->
                        <div class="mb-4">
                            <h6 class="mb-3">选择号码（最多10个）：</h6>
                            <div id="numberSelector" class="number-selector">
                                <!-- 1-80号码圆圈将通过JavaScript生成 -->
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">已选择：<span id="selectedCount">0</span>/10 个号码</small>
                            </div>
                        </div>

                        <!-- 期数选择和筛选条件 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="periodRange" class="form-label">分析最近期数：</label>
                                <input type="number" class="form-control" id="periodRange" min="1" max="500" value="100" placeholder="输入期数">
                                <small class="text-muted">范围：1-500期</small>
                            </div>
                            <div class="col-md-3">
                                <label for="minMatches" class="form-label">最少匹配数量：</label>
                                <select class="form-control" id="minMatches">
                                    <option value="0">显示全部</option>
                                    <option value="1">至少1个</option>
                                    <option value="2">至少2个</option>
                                    <option value="3">至少3个</option>
                                    <option value="4">至少4个</option>
                                    <option value="5">至少5个</option>
                                </select>
                                <small class="text-muted">筛选匹配结果</small>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="button" class="btn btn-primary" onclick="analyzeNumbers()" id="analyzeBtn">
                                    <i class="fas fa-chart-line me-1"></i>开始分析
                                </button>
                            </div>
                        </div>

                        <!-- 分析结果区域 -->
                        <div id="analysisResults" style="display: none;">
                            <!-- 统计概览 -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h5 class="card-title" id="totalMatches">0</h5>
                                            <p class="card-text">总匹配期数</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h5 class="card-title" id="fullMatches">0</h5>
                                            <p class="card-text">全中期数</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h5 class="card-title" id="partialMatches">0</h5>
                                            <p class="card-text">部分匹配期数</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h5 class="card-title" id="matchRate">0%</h5>
                                            <p class="card-text">匹配率</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 详细结果表格 -->
                            <div class="card">
                                <div class="card-header">
                                    <i class="fas fa-table me-2"></i>
                                    详细匹配结果 <span id="filteredCount" class="badge bg-secondary ms-2">0</span>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-dark sticky-top">
                                                <tr>
                                                    <th>期号</th>
                                                    <th>开奖号码</th>
                                                    <th>匹配数量</th>
                                                    <th>匹配号码</th>
                                                    <th>匹配率</th>
                                                </tr>
                                            </thead>
                                            <tbody id="resultsTableBody">
                                                <!-- 结果将通过JavaScript填充 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- 统计图表 -->
                            <div class="row mt-4">
                                <div class="col-md-8 mx-auto">
                                    <div class="card">
                                        <div class="card-header">
                                            <i class="fas fa-chart-pie me-2"></i>
                                            匹配数量分布
                                        </div>
                                        <div class="card-body">
                                            <div id="matchDistributionChart" style="height: 400px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                <i class="fas fa-info-circle me-1"></i>
                数据仅供参考，彩票具有随机性，请理性购彩
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let selectedNumbers = [];
        let currentAnalysisResult = null; // 保存当前分析结果

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            checkRefreshStatus(); // 检查刷新状态
            initializeNumberSelector(); // 初始化号码选择器

            // 添加筛选条件变化监听器
            document.getElementById('minMatches').addEventListener('change', function() {
                if (currentAnalysisResult) {
                    displayAnalysisResults(currentAnalysisResult);
                }
            });
        });

        // 加载所有数据
        async function loadAllData() {
            try {
                await Promise.all([
                    loadBasicStats(),
                    loadLatestResults(),
                    loadFrequencyChart(),
                    loadTrendChart()
                ]);
            } catch (error) {
                console.error('加载数据失败:', error);
                showError('数据加载失败，请检查服务器连接');
            }
        }

        // 加载基本统计信息
        async function loadBasicStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();

                document.getElementById('totalPeriods').textContent = data.total_periods;
                document.getElementById('latestPeriod').textContent = data.latest_period;
                document.getElementById('earliestPeriod').textContent = data.earliest_period;
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载最新开奖结果
        async function loadLatestResults() {
            try {
                const response = await fetch('/api/results?limit=5');
                const data = await response.json();

                let html = '';
                data.forEach(result => {
                    html += `
                        <div class="mb-3 p-3 border rounded">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>期号:</strong> ${result.period}
                                </div>
                                <div class="col-md-9">
                                    <strong>开奖号码:</strong><br>
                                    ${result.numbers.map(num => `<span class="number-badge">${num.toString().padStart(2, '0')}</span>`).join('')}
                                </div>
                            </div>
                        </div>
                    `;
                });

                document.getElementById('latestResults').innerHTML = html;
            } catch (error) {
                console.error('加载开奖结果失败:', error);
                document.getElementById('latestResults').innerHTML = '<p class="text-danger">加载失败</p>';
            }
        }

        // 加载频率图表
        async function loadFrequencyChart() {
            try {
                const response = await fetch('/api/charts/frequency?top_n=20');
                const data = await response.json();
                const chartData = JSON.parse(data.chart);
                
                Plotly.newPlot('frequencyChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('加载频率图表失败:', error);
            }
        }

        // 加载趋势图表
        async function loadTrendChart() {
            try {
                const response = await fetch('/api/charts/trends?periods=50');
                const data = await response.json();
                const chartData = JSON.parse(data.chart);
                
                Plotly.newPlot('trendChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('加载趋势图表失败:', error);
            }
        }

        // 初始化号码选择器
        function initializeNumberSelector() {
            const selector = document.getElementById('numberSelector');
            selector.innerHTML = '';

            for (let i = 1; i <= 80; i++) {
                const circle = document.createElement('div');
                circle.className = 'number-circle';
                circle.textContent = i.toString().padStart(2, '0');
                circle.dataset.number = i;
                circle.addEventListener('click', () => toggleNumber(i));
                selector.appendChild(circle);
            }
        }

        // 切换号码选择状态
        function toggleNumber(number) {
            const circle = document.querySelector(`[data-number="${number}"]`);

            if (selectedNumbers.includes(number)) {
                // 取消选择
                selectedNumbers = selectedNumbers.filter(n => n !== number);
                circle.classList.remove('selected');
                updateDisabledState();
            } else {
                // 选择号码
                if (selectedNumbers.length < 10) {
                    selectedNumbers.push(number);
                    circle.classList.add('selected');
                    updateDisabledState();
                }
            }

            updateSelectedCount();
        }

        // 更新禁用状态
        function updateDisabledState() {
            const circles = document.querySelectorAll('.number-circle');
            circles.forEach(circle => {
                const number = parseInt(circle.dataset.number);
                if (selectedNumbers.length >= 10 && !selectedNumbers.includes(number)) {
                    circle.classList.add('disabled');
                } else {
                    circle.classList.remove('disabled');
                }
            });
        }

        // 更新选择计数
        function updateSelectedCount() {
            document.getElementById('selectedCount').textContent = selectedNumbers.length;
        }

        // 分析选中的号码
        async function analyzeNumbers() {
            if (selectedNumbers.length === 0) {
                showError('请至少选择一个号码');
                return;
            }

            const periodRange = parseInt(document.getElementById('periodRange').value);
            if (!periodRange || periodRange < 1 || periodRange > 500) {
                showError('请输入有效的期数范围（1-500）');
                return;
            }

            const btn = document.getElementById('analyzeBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>分析中...';
            btn.disabled = true;

            try {
                const response = await fetch('/api/analyze-numbers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        numbers: selectedNumbers,
                        periods: periodRange
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                currentAnalysisResult = result; // 保存分析结果
                displayAnalysisResults(result);

            } catch (error) {
                console.error('分析失败:', error);
                showError('分析失败，请检查网络连接');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        // 显示分析结果
        function displayAnalysisResults(result) {
            // 显示结果区域
            document.getElementById('analysisResults').style.display = 'block';

            // 获取筛选条件
            const minMatches = parseInt(document.getElementById('minMatches').value);

            // 筛选详细结果
            const filteredDetails = result.details.filter(detail => detail.match_count >= minMatches);

            // 计算筛选后的统计数据
            const filteredStats = calculateFilteredStats(filteredDetails, result.details.length);

            // 更新统计概览（基于筛选后的数据）
            document.getElementById('totalMatches').textContent = filteredStats.totalMatches;
            document.getElementById('fullMatches').textContent = filteredStats.fullMatches;
            document.getElementById('partialMatches').textContent = filteredStats.partialMatches;
            document.getElementById('matchRate').textContent = filteredStats.matchRate + '%';

            // 更新筛选计数
            document.getElementById('filteredCount').textContent = filteredDetails.length;

            // 更新详细结果表格
            const tbody = document.getElementById('resultsTableBody');
            tbody.innerHTML = '';

            filteredDetails.forEach(detail => {
                const row = document.createElement('tr');

                // 匹配类型样式
                let matchClass = 'match-none';
                if (detail.match_count === selectedNumbers.length) {
                    matchClass = 'match-full';
                } else if (detail.match_count > 0) {
                    matchClass = 'match-partial';
                }

                row.innerHTML = `
                    <td>${detail.period}</td>
                    <td>${detail.numbers.map(num => `<span class="number-badge">${num.toString().padStart(2, '0')}</span>`).join('')}</td>
                    <td><span class="match-badge ${matchClass}">${detail.match_count}</span></td>
                    <td>${detail.matched_numbers.map(num => `<span class="number-badge">${num.toString().padStart(2, '0')}</span>`).join('')}</td>
                    <td>${detail.match_rate}%</td>
                `;
                tbody.appendChild(row);
            });

            // 绘制统计图表（基于筛选后的数据）
            drawMatchDistributionChart(result.distribution, filteredDetails);

            // 滚动到结果区域
            document.getElementById('analysisResults').scrollIntoView({ behavior: 'smooth' });
        }

        // 计算筛选后的统计数据
        function calculateFilteredStats(filteredDetails, totalPeriods) {
            let totalMatches = 0;
            let fullMatches = 0;
            let partialMatches = 0;

            filteredDetails.forEach(detail => {
                if (detail.match_count > 0) {
                    totalMatches++;
                    if (detail.match_count === selectedNumbers.length) {
                        fullMatches++;
                    } else {
                        partialMatches++;
                    }
                }
            });

            // 匹配率基于筛选后的结果数量
            const matchRate = filteredDetails.length > 0 ?
                Math.round((totalMatches / filteredDetails.length) * 100) : 0;

            return {
                totalMatches,
                fullMatches,
                partialMatches,
                matchRate
            };
        }

        // 绘制匹配数量分布图
        function drawMatchDistributionChart(originalDistribution, filteredDetails) {
            // 计算筛选后的分布
            const filteredDistribution = {};

            // 初始化分布统计
            for (let i = 0; i <= selectedNumbers.length; i++) {
                filteredDistribution[i] = 0;
            }

            // 统计筛选后的数据
            filteredDetails.forEach(detail => {
                filteredDistribution[detail.match_count]++;
            });

            // 过滤掉数量为0的项目，使图表更清晰
            const nonZeroDistribution = {};
            Object.keys(filteredDistribution).forEach(key => {
                if (filteredDistribution[key] > 0) {
                    nonZeroDistribution[key] = filteredDistribution[key];
                }
            });

            const data = [{
                values: Object.values(nonZeroDistribution),
                labels: Object.keys(nonZeroDistribution).map(key => `匹配${key}个`),
                type: 'pie',
                hole: 0.4,
                marker: {
                    colors: ['#6c757d', '#ffc107', '#fd7e14', '#28a745', '#17a2b8', '#6f42c1', '#e83e8c', '#20c997', '#fd7e14', '#6610f2']
                }
            }];

            const layout = {
                title: '筛选后匹配数量分布',
                showlegend: true,
                margin: { t: 40, b: 40, l: 40, r: 40 }
            };

            Plotly.newPlot('matchDistributionChart', data, layout, {responsive: true});
        }



        // 检查刷新状态
        async function checkRefreshStatus() {
            try {
                const response = await fetch('/api/refresh/status');
                const status = await response.json();

                const btn = document.querySelector('.btn-refresh');
                if (!status.can_refresh) {
                    btn.disabled = true;
                    btn.innerHTML = `<i class="fas fa-clock me-1"></i>等待 ${status.remaining_time}s`;

                    // 每秒更新倒计时
                    const countdown = setInterval(async () => {
                        const newStatus = await fetch('/api/refresh/status').then(r => r.json());
                        if (newStatus.can_refresh) {
                            btn.disabled = false;
                            btn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新数据';
                            clearInterval(countdown);
                        } else {
                            btn.innerHTML = `<i class="fas fa-clock me-1"></i>等待 ${newStatus.remaining_time}s`;
                        }
                    }, 1000);
                } else {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新数据';
                }
            } catch (error) {
                console.error('检查刷新状态失败:', error);
            }
        }

        // 刷新数据
        async function refreshData() {
            const btn = document.querySelector('.btn-refresh');
            const originalText = btn.innerHTML;

            // 先检查是否可以刷新
            try {
                const statusResponse = await fetch('/api/refresh/status');
                const status = await statusResponse.json();

                if (!status.can_refresh) {
                    showError(`数据刷新过于频繁，请等待 ${status.remaining_time} 秒后再试`);
                    return;
                }
            } catch (error) {
                console.error('检查刷新状态失败:', error);
            }

            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
            btn.disabled = true;

            try {
                const response = await fetch('/api/refresh', {method: 'POST'});

                if (response.status === 429) {
                    // 刷新过于频繁
                    const result = await response.json();
                    showError(result.detail || '数据刷新过于频繁，请稍后再试');
                    return;
                }

                const result = await response.json();

                if (result.success) {
                    showSuccess('数据刷新成功！');
                    // 重新加载所有数据
                    await loadAllData();
                } else {
                    showError(result.message || '数据刷新失败');
                }
            } catch (error) {
                console.error('刷新数据失败:', error);
                if (error.message.includes('429')) {
                    showError('数据刷新过于频繁，请稍后再试');
                } else {
                    showError('刷新数据失败，请检查网络连接');
                }
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
                // 重新检查刷新状态
                setTimeout(checkRefreshStatus, 1000);
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }

        // 显示错误消息
        function showError(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }
    </script>
</body>
</html>
