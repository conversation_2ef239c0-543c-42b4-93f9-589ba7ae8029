<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快乐8数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .number-badge {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .hot-number {
            background: #dc3545;
        }
        .cold-number {
            background: #6c757d;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .btn-refresh {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
        }
        .btn-refresh:hover {
            background: linear-gradient(135deg, #43a3f5 0%, #00e8f5 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                快乐8数据分析系统
            </a>
            <button class="btn btn-refresh" onclick="refreshData()">
                <i class="fas fa-sync-alt me-1"></i>
                刷新数据
            </button>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 基本统计信息 -->
        <div class="row" id="statsSection">
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <h3 id="totalPeriods">-</h3>
                    <p>总期数</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <h3 id="latestPeriod">-</h3>
                    <p>最新期号</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card text-center">
                    <h3 id="earliestPeriod">-</h3>
                    <p>最早期号</p>
                </div>
            </div>
        </div>

        <!-- 最新开奖结果 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-trophy me-2"></i>
                最新开奖结果
            </div>
            <div class="card-body">
                <div id="latestResults" class="loading">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row">
            <!-- 号码频率图表 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-bar me-2"></i>
                        号码出现频率 TOP 20
                    </div>
                    <div class="card-body">
                        <div id="frequencyChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>

            <!-- 最近趋势图表 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-line me-2"></i>
                        最近50期热门号码
                    </div>
                    <div class="card-body">
                        <div id="trendChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                <i class="fas fa-info-circle me-1"></i>
                数据仅供参考，彩票具有随机性，请理性购彩
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            checkRefreshStatus(); // 检查刷新状态
        });

        // 加载所有数据
        async function loadAllData() {
            try {
                await Promise.all([
                    loadBasicStats(),
                    loadLatestResults(),
                    loadFrequencyChart(),
                    loadTrendChart()
                ]);
            } catch (error) {
                console.error('加载数据失败:', error);
                showError('数据加载失败，请检查服务器连接');
            }
        }

        // 加载基本统计信息
        async function loadBasicStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();

                document.getElementById('totalPeriods').textContent = data.total_periods;
                document.getElementById('latestPeriod').textContent = data.latest_period;
                document.getElementById('earliestPeriod').textContent = data.earliest_period;
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载最新开奖结果
        async function loadLatestResults() {
            try {
                const response = await fetch('/api/results?limit=5');
                const data = await response.json();

                let html = '';
                data.forEach(result => {
                    html += `
                        <div class="mb-3 p-3 border rounded">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>期号:</strong> ${result.period}
                                </div>
                                <div class="col-md-9">
                                    <strong>开奖号码:</strong><br>
                                    ${result.numbers.map(num => `<span class="number-badge">${num.toString().padStart(2, '0')}</span>`).join('')}
                                </div>
                            </div>
                        </div>
                    `;
                });

                document.getElementById('latestResults').innerHTML = html;
            } catch (error) {
                console.error('加载开奖结果失败:', error);
                document.getElementById('latestResults').innerHTML = '<p class="text-danger">加载失败</p>';
            }
        }

        // 加载频率图表
        async function loadFrequencyChart() {
            try {
                const response = await fetch('/api/charts/frequency?top_n=20');
                const data = await response.json();
                const chartData = JSON.parse(data.chart);
                
                Plotly.newPlot('frequencyChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('加载频率图表失败:', error);
            }
        }

        // 加载趋势图表
        async function loadTrendChart() {
            try {
                const response = await fetch('/api/charts/trends?periods=50');
                const data = await response.json();
                const chartData = JSON.parse(data.chart);
                
                Plotly.newPlot('trendChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('加载趋势图表失败:', error);
            }
        }



        // 检查刷新状态
        async function checkRefreshStatus() {
            try {
                const response = await fetch('/api/refresh/status');
                const status = await response.json();

                const btn = document.querySelector('.btn-refresh');
                if (!status.can_refresh) {
                    btn.disabled = true;
                    btn.innerHTML = `<i class="fas fa-clock me-1"></i>等待 ${status.remaining_time}s`;

                    // 每秒更新倒计时
                    const countdown = setInterval(async () => {
                        const newStatus = await fetch('/api/refresh/status').then(r => r.json());
                        if (newStatus.can_refresh) {
                            btn.disabled = false;
                            btn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新数据';
                            clearInterval(countdown);
                        } else {
                            btn.innerHTML = `<i class="fas fa-clock me-1"></i>等待 ${newStatus.remaining_time}s`;
                        }
                    }, 1000);
                } else {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新数据';
                }
            } catch (error) {
                console.error('检查刷新状态失败:', error);
            }
        }

        // 刷新数据
        async function refreshData() {
            const btn = document.querySelector('.btn-refresh');
            const originalText = btn.innerHTML;

            // 先检查是否可以刷新
            try {
                const statusResponse = await fetch('/api/refresh/status');
                const status = await statusResponse.json();

                if (!status.can_refresh) {
                    showError(`数据刷新过于频繁，请等待 ${status.remaining_time} 秒后再试`);
                    return;
                }
            } catch (error) {
                console.error('检查刷新状态失败:', error);
            }

            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
            btn.disabled = true;

            try {
                const response = await fetch('/api/refresh', {method: 'POST'});

                if (response.status === 429) {
                    // 刷新过于频繁
                    const result = await response.json();
                    showError(result.detail || '数据刷新过于频繁，请稍后再试');
                    return;
                }

                const result = await response.json();

                if (result.success) {
                    showSuccess('数据刷新成功！');
                    // 重新加载所有数据
                    await loadAllData();
                } else {
                    showError(result.message || '数据刷新失败');
                }
            } catch (error) {
                console.error('刷新数据失败:', error);
                if (error.message.includes('429')) {
                    showError('数据刷新过于频繁，请稍后再试');
                } else {
                    showError('刷新数据失败，请检查网络连接');
                }
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
                // 重新检查刷新状态
                setTimeout(checkRefreshStatus, 1000);
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }

        // 显示错误消息
        function showError(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }
    </script>
</body>
</html>
