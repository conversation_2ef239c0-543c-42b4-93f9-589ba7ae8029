#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8数据分析Web服务
基于FastAPI的Web应用
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
from typing import List, Optional
import os
import uvicorn

from data_service import Happy8DataService

# 创建FastAPI应用
app = FastAPI(
    title="快乐8数据分析系统",
    description="福彩快乐8历史开奖数据分析和可视化平台",
    version="1.0.0"
)

# 配置模板和静态文件
templates = Jinja2Templates(directory="templates")

# 创建静态文件目录（如果不存在）
if not os.path.exists("static"):
    os.makedirs("static")

app.mount("/static", StaticFiles(directory="static"), name="static")

# 初始化数据服务
data_service = Happy8DataService()

# 响应模型
class BasicStatsResponse(BaseModel):
    total_periods: int
    latest_period: Optional[str]
    earliest_period: Optional[str]

class NumberFrequencyResponse(BaseModel):
    number: int
    count: int
    frequency: float

class LotteryResultResponse(BaseModel):
    period: str
    numbers: List[int]
    numbers_str: str
    count: int

class AnalyzeNumbersRequest(BaseModel):
    numbers: List[int]
    periods: int

class AnalysisDetailResponse(BaseModel):
    period: str
    numbers: List[int]
    match_count: int
    matched_numbers: List[int]
    match_rate: float

class AnalysisSummaryResponse(BaseModel):
    total_matches: int
    full_matches: int
    partial_matches: int
    match_rate: float

class AnalyzeNumbersResponse(BaseModel):
    summary: AnalysisSummaryResponse
    details: List[AnalysisDetailResponse]
    distribution: dict
    trend: List[dict]



# 路由定义

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/api/stats", response_model=BasicStatsResponse)
async def get_basic_stats():
    """获取基本统计信息"""
    try:
        stats = data_service.get_basic_stats()
        if not stats:
            raise HTTPException(status_code=404, detail="数据未找到")
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@app.get("/api/frequency", response_model=List[NumberFrequencyResponse])
async def get_number_frequency(limit: int = 20):
    """获取号码出现频率"""
    try:
        frequency = data_service.get_number_frequency(limit)
        return frequency
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取频率数据失败: {str(e)}")

@app.get("/api/trends", response_model=List[NumberFrequencyResponse])
async def get_recent_trends(periods: int = 50):
    """获取最近趋势"""
    try:
        trends = data_service.get_recent_trends(periods)
        return trends
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取趋势数据失败: {str(e)}")

@app.get("/api/results", response_model=List[LotteryResultResponse])
async def get_latest_results(limit: int = 10):
    """获取最新开奖结果"""
    try:
        results = data_service.get_latest_results(limit)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取开奖结果失败: {str(e)}")

@app.get("/api/charts/frequency")
async def get_frequency_chart(top_n: int = 20):
    """获取频率图表数据"""
    try:
        chart_data = data_service.create_frequency_chart(top_n)
        return JSONResponse(content={"chart": chart_data})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成频率图表失败: {str(e)}")

@app.get("/api/charts/trends")
async def get_trend_chart(periods: int = 50):
    """获取趋势图表数据"""
    try:
        chart_data = data_service.create_trend_chart(periods)
        return JSONResponse(content={"chart": chart_data})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成趋势图表失败: {str(e)}")

@app.post("/api/refresh")
async def refresh_data():
    """刷新数据"""
    try:
        result = data_service.refresh_data()
        if result['success']:
            return result
        else:
            # 如果是因为缓存限制失败，返回429状态码
            if 'remaining_time' in result and result['remaining_time'] > 0:
                raise HTTPException(
                    status_code=429,
                    detail=result['message'],
                    headers={"Retry-After": str(result['remaining_time'])}
                )
            else:
                raise HTTPException(status_code=500, detail=result['message'])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刷新数据失败: {str(e)}")

@app.get("/api/refresh/status")
async def get_refresh_status():
    """获取刷新状态"""
    try:
        remaining_time = data_service.get_refresh_remaining_time()
        can_refresh = data_service._can_refresh()
        return {
            "can_refresh": can_refresh,
            "remaining_time": remaining_time,
            "message": f"距离下次可刷新还有 {remaining_time} 秒" if not can_refresh else "可以刷新"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取刷新状态失败: {str(e)}")

@app.post("/api/analyze-numbers", response_model=AnalyzeNumbersResponse)
async def analyze_numbers(request: AnalyzeNumbersRequest):
    """分析选中号码的出现情况"""
    try:
        # 验证输入
        if not request.numbers or len(request.numbers) > 4:
            raise HTTPException(status_code=400, detail="请选择1-4个号码")

        if request.periods < 1 or request.periods > 500:
            raise HTTPException(status_code=400, detail="期数范围应在1-500之间")

        for num in request.numbers:
            if num < 1 or num > 80:
                raise HTTPException(status_code=400, detail="号码应在1-80之间")

        # 调用数据服务进行分析
        result = data_service.analyze_selected_numbers(request.numbers, request.periods)
        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "快乐8数据分析系统",
        "version": "1.0.0",
        "data_loaded": data_service.df is not None
    }

# 错误处理
@app.exception_handler(404)
async def not_found_handler(request: Request, _: HTTPException):
    return templates.TemplateResponse(
        "error.html",
        {"request": request, "error": "页面未找到", "status_code": 404}
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, _: HTTPException):
    return templates.TemplateResponse(
        "error.html",
        {"request": request, "error": "服务器内部错误", "status_code": 500}
    )

if __name__ == "__main__":
    # 检查数据文件是否存在
    if not os.path.exists("happy8_data.csv"):
        print("警告: 数据文件不存在，请先运行爬虫脚本获取数据")
        print("运行命令: python happy8_crawler.py")
    
    # 启动服务
    print("启动快乐8数据分析Web服务...")
    print("访问地址: http://localhost:8000")
    
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
