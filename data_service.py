#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8数据服务模块
提供数据分析和处理功能
"""

import pandas as pd
import json
import os
from collections import Counter
from typing import List, Dict
import plotly.graph_objects as go
from plotly.utils import PlotlyJSONEncoder
import time

class Happy8DataService:
    def __init__(self, data_file: str = 'happy8_data.csv'):
        self.data_file = data_file
        self.df = None
        self._cache = {}

        # 从配置文件获取缓存时间
        try:
            from config import get_config
            config = get_config()
            self._cache_timeout = config.CACHE_TIMEOUT  # 10分钟缓存
            self._refresh_timeout = config.REFRESH_TIMEOUT  # 10分钟刷新限制
        except:
            self._cache_timeout = 600  # 默认10分钟缓存
            self._refresh_timeout = 600  # 默认10分钟刷新限制

        self._refresh_cache = {}  # 刷新操作缓存
        self.load_data()

    def _get_cache_key(self, method_name: str, *args, **kwargs) -> str:
        """生成缓存键"""
        return f"{method_name}_{hash(str(args) + str(sorted(kwargs.items())))}"

    def _get_cached_result(self, cache_key: str):
        """获取缓存结果"""
        if cache_key in self._cache:
            result, timestamp = self._cache[cache_key]
            if time.time() - timestamp < self._cache_timeout:
                return result
            else:
                del self._cache[cache_key]
        return None

    def _set_cache(self, cache_key: str, result):
        """设置缓存"""
        self._cache[cache_key] = (result, time.time())

    def clear_cache(self):
        """清除缓存"""
        self._cache.clear()

    def _can_refresh(self) -> bool:
        """检查是否可以刷新数据（10分钟限制）"""
        last_refresh = self._refresh_cache.get('last_refresh_time', 0)
        return time.time() - last_refresh >= self._refresh_timeout

    def _set_refresh_time(self):
        """设置最后刷新时间"""
        self._refresh_cache['last_refresh_time'] = time.time()

    def get_refresh_remaining_time(self) -> int:
        """获取距离下次可刷新的剩余时间（秒）"""
        last_refresh = self._refresh_cache.get('last_refresh_time', 0)
        elapsed = time.time() - last_refresh
        remaining = max(0, self._refresh_timeout - elapsed)
        return int(remaining)

    def load_data(self) -> bool:
        """加载开奖数据"""
        try:
            if os.path.exists(self.data_file):
                self.df = pd.read_csv(self.data_file)

                # 修复时间顺序：按期号降序排列，最新的在前面
                self.df = self.df.sort_values('period', ascending=False).reset_index(drop=True)

                print(f"成功加载 {len(self.df)} 期开奖数据")
                return True
            else:
                print(f"数据文件 {self.data_file} 不存在")
                return False
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def get_basic_stats(self) -> Dict:
        """获取基本统计信息"""
        if self.df is None:
            return {}

        return {
            'total_periods': int(len(self.df)),
            'latest_period': str(self.df.iloc[0]['period']) if len(self.df) > 0 else None,
            'earliest_period': str(self.df.iloc[-1]['period']) if len(self.df) > 0 else None
        }
    
    def get_number_frequency(self, limit: int = 80) -> List[Dict]:
        """获取号码出现频率"""
        if self.df is None:
            return []

        all_numbers = []
        for _, row in self.df.iterrows():
            numbers = eval(row['numbers'])

            # 确保每期只有20个号码，如果超过20个就从后往前取20个
            if len(numbers) > 20:
                numbers = numbers[-20:]

            all_numbers.extend([int(num) for num in numbers])

        frequency = Counter(all_numbers)
        total_periods = len(self.df)

        result = []
        for num, count in frequency.most_common(limit):
            result.append({
                'number': num,
                'count': count,
                'frequency': round((count / total_periods) * 100, 2)
            })

        return result
    
    def get_recent_trends(self, periods: int = 50) -> List[Dict]:
        """获取最近趋势（现在数据已按期号降序排列，head()获取的就是最新数据）"""
        if self.df is None:
            return []

        # 获取最近的periods期数据（现在已经是按期号降序排列）
        recent_df = self.df.head(periods)
        recent_numbers = []

        for _, row in recent_df.iterrows():
            numbers = eval(row['numbers'])

            # 确保每期只有20个号码，如果超过20个就从后往前取20个
            if len(numbers) > 20:
                numbers = numbers[-20:]

            recent_numbers.extend([int(num) for num in numbers])

        frequency = Counter(recent_numbers)
        total_periods = len(recent_df)

        result = []
        for num, count in frequency.most_common(20):
            result.append({
                'number': num,
                'count': count,
                'frequency': round((count / total_periods) * 100, 2)
            })

        return result
    
    def get_latest_results(self, limit: int = 10) -> List[Dict]:
        """获取最新开奖结果"""
        if self.df is None:
            return []

        results = []
        for i in range(min(limit, len(self.df))):
            row = self.df.iloc[i]
            numbers = eval(row['numbers'])

            # 确保每期只有20个号码，如果超过20个就从后往前取20个
            if len(numbers) > 20:
                numbers = numbers[-20:]
                print(f"期号 {row['period']} 号码超过20个，已截取最后20个")

            results.append({
                'period': str(row['period']),
                'numbers': [int(num) for num in numbers],
                'numbers_str': ' '.join([f"{int(num):02d}" for num in numbers]),
                'count': len(numbers)
            })

        return results
    

    
    def create_frequency_chart(self, top_n: int = 20) -> str:
        """创建号码频率图表（带缓存）"""
        cache_key = self._get_cache_key('frequency_chart', top_n)
        cached_result = self._get_cached_result(cache_key)
        if cached_result is not None:
            return cached_result

        frequency_data = self.get_number_frequency(top_n)

        if not frequency_data:
            return "{}"

        numbers = [f"{item['number']:02d}" for item in frequency_data]
        frequencies = [item['frequency'] for item in frequency_data]

        fig = go.Figure(data=[
            go.Bar(
                x=numbers,
                y=frequencies,
                text=[f"{freq}%" for freq in frequencies],
                textposition='auto',
                marker_color='rgba(55, 128, 191, 0.7)',
                marker_line_color='rgba(55, 128, 191, 1.0)',
                marker_line_width=1.5
            )
        ])

        fig.update_layout(
            title=f'快乐8号码出现频率 TOP {top_n}',
            xaxis_title='号码',
            yaxis_title='出现频率 (%)',
            template='plotly_white',
            height=400
        )

        result = json.dumps(fig, cls=PlotlyJSONEncoder)
        self._set_cache(cache_key, result)
        return result
    
    def create_trend_chart(self, periods: int = 50) -> str:
        """创建最近趋势图表"""
        trend_data = self.get_recent_trends(periods)
        
        if not trend_data:
            return "{}"
        
        numbers = [f"{item['number']:02d}" for item in trend_data[:15]]
        frequencies = [item['frequency'] for item in trend_data[:15]]
        
        fig = go.Figure(data=[
            go.Bar(
                x=numbers,
                y=frequencies,
                text=[f"{freq}%" for freq in frequencies],
                textposition='auto',
                marker_color='rgba(255, 127, 14, 0.7)',
                marker_line_color='rgba(255, 127, 14, 1.0)',
                marker_line_width=1.5
            )
        ])
        
        fig.update_layout(
            title=f'最近{periods}期热门号码 TOP 15',
            xaxis_title='号码',
            yaxis_title='出现频率 (%)',
            template='plotly_white',
            height=400
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    

    
    def refresh_data(self) -> Dict[str, any]:
        """刷新数据（重新运行爬虫）带缓存限制"""
        # 检查是否可以刷新
        if not self._can_refresh():
            remaining_time = self.get_refresh_remaining_time()
            return {
                'success': False,
                'message': f'数据刷新过于频繁，请等待 {remaining_time} 秒后再试',
                'remaining_time': remaining_time
            }

        try:
            print("开始刷新数据...")
            from happy8_crawler import Happy8Crawler
            crawler = Happy8Crawler()
            crawler.run(500)

            # 设置刷新时间
            self._set_refresh_time()

            # 清除缓存
            self.clear_cache()

            # 重新加载数据
            success = self.load_data()

            if success:
                return {
                    'success': True,
                    'message': '数据刷新成功',
                    'remaining_time': 0
                }
            else:
                return {
                    'success': False,
                    'message': '数据加载失败',
                    'remaining_time': 0
                }

        except Exception as e:
            print(f"刷新数据失败: {e}")
            return {
                'success': False,
                'message': f'刷新数据失败: {str(e)}',
                'remaining_time': 0
            }
